#! /usr/bin/env python3
import os
import sys
import numpy as np
import open3d as o3d
import importlib
import scipy.io as scio
from PIL import Image

import torch
from graspnetAPI import GraspGroup
from scipy.spatial.transform import Rotation as R

from models.backbone import Pointnet2Backbone
from models.modules import ToleranceNet
from models.graspnet import GraspNet, pred_decode
from utils.collision_detector import ModelFreeCollisionDetector
from utils.data_utils import CameraInfo, create_point_cloud_from_depth_image


class GN():
    def __init__(self, checkpoint_path, num_point = 20000, num_view = 300, collision_thresh = 0.001, empty_thresh = 0.15, voxel_size = 0.01):
        self.checkpoint_path = checkpoint_path
        self.num_point = num_point
        self.num_view = num_view
        self.collision_thresh = collision_thresh
        self.empty_thresh = empty_thresh
        self.voxel_size = voxel_size
        
        self.net, self.pc_net, self.tol_net = self.get_net()

    def get_net(self):
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        # Init the whole model
        net = GraspNet(input_feature_dim=0, num_view=self.num_view, num_angle=12, num_depth=4,
                cylinder_radius=0.05, hmin=-0.02, hmax_list=[0.01,0.02,0.03,0.04], is_training=False)
        net.to(device)
        # Load checkpoint
        checkpoint = torch.load(self.checkpoint_path)
        net.load_state_dict(checkpoint['model_state_dict'])
        start_epoch = checkpoint['epoch']
        print("-> loaded checkpoint %s (epoch: %d)"%(self.checkpoint_path, start_epoch))
        # set model to eval mode
        net.eval()

        # Load network parameters
        whole_dict = checkpoint['model_state_dict']

        # Init the point cloud processing model
        pc_net = Pointnet2Backbone(input_feature_dim=0)
        pc_net.to(device)
        pc_dict = pc_net.state_dict()
        filter_dict = {k: v for k, v in whole_dict.items() if k in pc_dict} # filter out unnecessary keys
        pc_dict.update(filter_dict)
        pc_net.load_state_dict(pc_dict)
        pc_net.eval()

        # Init the tolerance model
        tol_net = ToleranceNet(num_angle=12, num_depth=4)
        tol_net.to(device)
        tol_dict = tol_net.state_dict()
        filter_dict = {k: v for k, v in whole_dict.items() if k in tol_dict} # filter out unnecessary keys
        tol_dict.update(filter_dict)
        tol_net.load_state_dict(tol_dict)
        tol_net.eval()

        return net, pc_net, tol_net

    def get_and_process_data(self, cloud):
        cloud = cloud.voxel_down_sample(0.001)

        cloud_masked = np.asarray(cloud.points)
        color_masked = np.asarray(cloud.colors)

        # sample points
        if len(cloud_masked) >= self.num_point:
            idxs = np.random.choice(len(cloud_masked), self.num_point, replace=False)
        else:
            idxs1 = np.arange(len(cloud_masked))
            idxs2 = np.random.choice(len(cloud_masked), self.num_point-len(cloud_masked), replace=True)
            idxs = np.concatenate([idxs1, idxs2], axis=0)
        cloud_sampled = cloud_masked[idxs]
        color_sampled = color_masked[idxs]

        # convert data
        cloud = o3d.geometry.PointCloud()
        cloud.points = o3d.utility.Vector3dVector(cloud_masked.astype(np.float32))
        cloud.colors = o3d.utility.Vector3dVector(color_masked.astype(np.float32))

        end_points = dict()
        cloud_sampled = torch.from_numpy(cloud_sampled[np.newaxis].astype(np.float32))
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        cloud_sampled = cloud_sampled.to(device)
        end_points['point_clouds'] = cloud_sampled
        end_points['cloud_colors'] = color_sampled

        return end_points, cloud

    def get_grasps(self, end_points_initial, end_points_target):
        # Forward pass
        with torch.no_grad():
            # Phase 1: Generate grasp hypotheses (Hypothesize)
            # Use only the initial data to generate ideal grasp candidates for the object
            end_points_initial = self.net(end_points_initial)
                        
            # Extract scene information from the processed target data
            pointcloud = end_points_target['input_xyz']  # Scene point cloud
            seed_xyz = end_points_target['fp2_xyz']      # Seed points in the scene
            
            # Extract grasp rotation information from the processed initial data
            grasp_top_views_rot = end_points_initial['grasp_top_view_rot']

            # Phase 2: Verification with the scene (Verify)
            # The crop function is key: it uses the target's seed points and point cloud,
            # but uses the initial's grasp directions to extract "viewpoint features" for each grasp hypothesis in the real scene
            vp_features = self.net.grasp_generator.crop(seed_xyz, pointcloud, grasp_top_views_rot)
            
            # ToleranceNet uses these scene-related features to refine and score the initial predictions
            end_points_integrated = self.tol_net(vp_features, end_points_initial)

            # Decode to obtain the final grasp poses
            grasp_preds = pred_decode(end_points_integrated)

        objectness_score = end_points_integrated['objectness_score']
        
        gg_array = grasp_preds[0].detach().cpu().numpy()
        gg = GraspGroup(gg_array)
        return gg

    def inference(self, o3d_pcd_initial, o3d_pcd_target):
        # 1. Process o3d_pcd_initial
        end_points_initial, _ = self.get_and_process_data(o3d_pcd_initial)

        # 2. Process o3d_pcd_target
        end_points_target, cloud = self.get_and_process_data(o3d_pcd_target)
        
        # 3. Extract scene features
        pointcloud = end_points_target['point_clouds']
        # `pc_net` only processes the target scene to extract seed points and features
        seed_features, seed_xyz, end_points_target = self.pc_net(pointcloud, end_points_target)

        # 4. Fuse both to generate final grasps
        gg = self.get_grasps(end_points_initial, end_points_target)
        
        # 5. Perform collision detection in the target scene
        if self.collision_thresh > 0:
            gg = self.collision_detection(gg, np.array(cloud.points))
        return gg

    def collision_detection(self, gg, cloud):
        mfcdetector = ModelFreeCollisionDetector(cloud, voxel_size=self.voxel_size)
        collision_mask = mfcdetector.detect(gg, approach_dist=0.05, collision_thresh=self.collision_thresh, empty_thresh=self.empty_thresh)
        gg = gg[~collision_mask]
        return gg


def transform_to_robot_coordinate(camera_extrinsic, grasp_translation, grasp_rotation_matrix):
    """
    Transform grasp pose from camera coordinate system to robot coordinate system.
    
    Args:
        camera_extrinsic (np.ndarray): 4x4 camera extrinsic matrix (camera to robot transform)
        grasp_translation (np.ndarray): 3x1 grasp translation vector in camera coordinates
        grasp_rotation_matrix (np.ndarray): 3x3 grasp rotation matrix in camera coordinates
        
    Returns:
        tuple: (robot_translation, robot_rotation_matrix, robot_euler_angles)
            - robot_translation (np.ndarray): 3x1 translation vector in robot coordinates
            - robot_rotation_matrix (np.ndarray): 3x3 rotation matrix in robot coordinates
            - robot_euler_angles (np.ndarray): 3x1 Euler angles (XYZ) in degrees
    """
    # Convert grasp pose to 4x4 homogeneous matrix
    grasp_matrix = np.eye(4)
    grasp_matrix[:3, :3] = grasp_rotation_matrix
    grasp_matrix[:3, 3] = grasp_translation
    
    # Apply transformation: robot_pose = camera_extrinsic * grasp_pose
    robot_pose_matrix = np.dot(camera_extrinsic, grasp_matrix)
    
    # Extract translation and rotation from transformed matrix
    robot_translation = robot_pose_matrix[:3, 3]
    robot_rotation_matrix = robot_pose_matrix[:3, :3]
    
    # Convert rotation matrix to Euler angles for easier interpretation
    robot_euler_angles = R.from_matrix(robot_rotation_matrix).as_euler('xyz', degrees=True)
    
    return robot_translation, robot_rotation_matrix, robot_euler_angles


def vis_grasps(gg, cloud, save_dir='results', max_grasps=50, camera_extrinsic=None):
    """
    Visualize grasps and save the scene to a PLY file.

    Args:
        gg: GraspGroup object containing grasp poses
        cloud: Open3D point cloud object
        save_dir: Directory to save the results (default: 'results')
        max_grasps: Maximum number of grasps to visualize (default: 50)
        camera_extrinsic: 4x4 camera extrinsic matrix for coordinate transformation (optional)
                         If provided, poses will be saved in both camera and world coordinates
    """
    # Apply NMS and sort by score
    gg.nms()
    gg.sort_by_score()
    gg = gg[:max_grasps]

    # Convert grasps to Open3D geometry
    grippers = gg.to_open3d_geometry_list()

    # Display visualization window (preserve original functionality)
    o3d.visualization.draw_geometries([cloud, *grippers])

    # Save results
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # Save cloud
    o3d.io.write_point_cloud(os.path.join(save_dir, 'scene.ply'), cloud)

    # Save grippers
    for i, gripper in enumerate(grippers):
        o3d.io.write_triangle_mesh(os.path.join(save_dir, f'gripper_{i:02d}.ply'), gripper)

    # Save grasp pose information to text file
    poses_file = os.path.join(save_dir, 'grasp_poses.txt')
    with open(poses_file, 'w') as f:
        # Write file header
        f.write("# Grasp Pose Information\n")
        if camera_extrinsic is not None:
            f.write("# This file contains poses in both camera and world coordinate systems\n")
            f.write("# Camera coordinate format: CAM_gripper_ID x y z roll pitch yaw\n")
            f.write("# World coordinate format:  WLD_gripper_ID x y z roll pitch yaw\n")
        else:
            f.write("# Camera coordinate format: gripper_ID x y z roll pitch yaw\n")
        f.write("# gripper_ID: gripper file number (00, 01, 02, ...)\n")
        f.write("# x, y, z: translation coordinates in meters\n")
        f.write("# roll, pitch, yaw: rotation angles in degrees (XYZ Euler)\n")
        f.write("#\n")
        
        # Extract pose information from selected grasps
        translations = gg.translations  # Shape: (N, 3)
        rotation_matrices = gg.rotation_matrices  # Shape: (N, 3, 3)
        
        # Write pose data for each grasp
        for i in range(len(gg)):
            # Extract camera coordinate pose
            cam_x, cam_y, cam_z = translations[i]
            cam_rotation_matrix = rotation_matrices[i]
            cam_rpy_angles = R.from_matrix(cam_rotation_matrix).as_euler('xyz', degrees=True)
            cam_roll, cam_pitch, cam_yaw = cam_rpy_angles
            
            # Write camera coordinate pose
            if camera_extrinsic is not None:
                f.write(f"CAM_{i:02d} {cam_x:.6f} {cam_y:.6f} {cam_z:.6f} {cam_roll:.2f} {cam_pitch:.2f} {cam_yaw:.2f}\n")
                
                # Transform to world coordinates and write
                world_translation, world_rotation_matrix, world_euler_angles = transform_to_robot_coordinate(
                    camera_extrinsic, translations[i], cam_rotation_matrix
                )
                world_x, world_y, world_z = world_translation
                world_roll, world_pitch, world_yaw = world_euler_angles
                f.write(f"WLD_{i:02d} {world_x:.6f} {world_y:.6f} {world_z:.6f} {world_roll:.2f} {world_pitch:.2f} {world_yaw:.2f}\n")
            else:
                # Original format for backward compatibility
                f.write(f"{i:02d} {cam_x:.6f} {cam_y:.6f} {cam_z:.6f} {cam_roll:.2f} {cam_pitch:.2f} {cam_yaw:.2f}\n")

    print(f'\nScene and {len(grippers)} grippers saved to {save_dir}')
    print(f'Grasp poses saved to {poses_file}')
