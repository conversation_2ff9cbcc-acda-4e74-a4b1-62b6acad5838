import open3d as o3d
import numpy as np
# Make sure graspnet_baseline.py is in your Python path
from graspnet_baseline import GraspNetBaseLine, vis_grasps

if __name__ == '__main__':
    # 1. Initialize GraspNet model
    # !!!IMPORTANT!!!: Replace this with your actual checkpoint file path (.tar)
    checkpoint_path = "/path/to/your/checkpoint.tar" 
    
    # Check if checkpoint file exists
    import os
    if not os.path.exists(checkpoint_path):
        print(f"Error: Checkpoint file not found at '{checkpoint_path}'")
        print("Please download a pretrained model and update the path.")
        exit()

    graspnet = GraspNetBaseLine(checkpoint_path=checkpoint_path)

    # 2. Load scene point cloud
    # !!!IMPORTANT!!!: Replace this with your actual point cloud file path (e.g. .pcd, .ply)
    pcd_path = "/path/to/your/scene.pcd"
    
    try:
        scene_pcd = o3d.io.read_point_cloud(pcd_path)
        if not scene_pcd.has_points():
            raise FileNotFoundError(f"Point cloud at '{pcd_path}' is empty or file not found.")
        # GraspNet requires colored point cloud, assign default gray if no color
        if not scene_pcd.has_colors():
            print("Point cloud has no colors, assigning gray as default.")
            scene_pcd.colors = o3d.utility.Vector3dVector(np.ones((len(scene_pcd.points), 3)) * 0.5)

    except Exception as e:
        print(f"Error loading point cloud: {e}")
        exit()

    # 3. Perform inference
    print("Performing grasp inference...")
    # gg is a graspnetAPI.GraspGroup object.
    gg = graspnet.inference(scene_pcd)

    # 4. Process and visualize results
    if len(gg) == 0:
        print("No grasp poses detected in the scene.")
    else:
        print(f"Successfully detected {len(gg)} grasp poses.")
        
        # Sort grasps by score
        # The sort_by_score() method sorts the grasps in place.
        gg.sort_by_score()

        # The first one is the best grasp
        best_grasp = gg[0]
        
        # --- Clarification on Grasp vs GraspGroup properties ---
        # `best_grasp` is a `Grasp` object, representing a SINGLE grasp.
        # We access its properties using singular names, e.g., `best_grasp.score`, which returns a single value.
        #
        # This is different from the `GraspGroup` object (`gg`), which represents a COLLECTION of grasps.
        # The `GraspGroup` object uses plural names, e.g., `gg.scores`, to return an array of ALL scores.
        #
        # The following lines extract the properties of the single best grasp for printing.
        grasp_score = best_grasp.score
        grasp_width = best_grasp.width
        grasp_translation = best_grasp.translation
        grasp_rotation = best_grasp.rotation_matrix
        
        # Print core information of the best grasp
        print("\n--- Best Grasp Pose ---")
        print(f"Score: {grasp_score:.4f}")
        print(f"Width: {grasp_width:.4f}")
        print(f"Translation (Position): {grasp_translation}")
        print(f"Rotation Matrix:\n{grasp_rotation}")

        # Visualize top 50 grasps by score
        print("\nVisualizing top 50 grasps... Close the visualization window to exit.")
        vis_grasps(gg, scene_pcd)
