# Pick and Place Inference Scripts

This directory contains inference scripts for the pick-and-place functionality using the GraspNet model.

## Files Overview

- `pick_and_place.py` - Core implementation of the pick-and-place GraspNet model (GN class)
- `pick_and_place_demo.py` - Full-featured demo script with comprehensive error handling
- `pick_and_place_simple.py` - Minimal example for quick testing
- `README_pick_and_place.md` - This documentation file

## Key Differences from Standard GraspNet

The pick-and-place implementation differs from the standard GraspNet baseline in several important ways:

1. **Dual Point Cloud Input**: Requires two point clouds:
   - Initial scene (where objects are picked from)
   - Target scene (where objects are placed to)

2. **Enhanced Architecture**: Uses separate networks for:
   - Point cloud processing (`pc_net`)
   - Tolerance estimation (`tol_net`)
   - Main GraspNet model (`net`)

3. **Contextual Grasp Generation**: Considers both scenes to generate contextually appropriate grasps

## Quick Start

### 1. Basic Usage

```python
from pick_and_place import GN, vis_grasps
import open3d as o3d

# Initialize the model
graspnet = GN(checkpoint_path='your_model.tar')

# Load point clouds
initial_pcd = o3d.io.read_point_cloud('initial_scene.pcd')
target_pcd = o3d.io.read_point_cloud('target_scene.pcd')

# Perform inference
grasps = graspnet.inference(initial_pcd, target_pcd)

# Visualize results
vis_grasps(grasps, initial_pcd)
```

### 2. Using the Demo Scripts

#### Full Demo Script
```bash
python pick_and_place_demo.py
```

#### Simple Demo Script
```bash
python pick_and_place_simple.py
```

## Prerequisites

1. **Model Checkpoint**: You need a trained model checkpoint file (`.tar` format)
2. **Point Cloud Files**: Two point cloud files in `.pcd` or `.ply` format
3. **Dependencies**: Same as the main GraspNet project

## Configuration Parameters

The `GN` class accepts the following parameters:

- `checkpoint_path`: Path to the trained model checkpoint
- `num_point`: Number of points to sample (default: 20000)
- `num_view`: Number of views for grasp generation (default: 300)
- `collision_thresh`: Collision detection threshold (default: 0.001)
- `empty_thresh`: Empty space threshold (default: 0.15)
- `voxel_size`: Voxel size for collision detection (default: 0.01)

## Input Requirements

### Point Cloud Format
- Supported formats: `.pcd`, `.ply`
- Must contain 3D coordinates
- Colors are recommended (will be assigned default gray if missing)

### Scene Setup
- **Initial Scene**: Should contain objects to be picked
- **Target Scene**: Should represent the placement environment
- Both scenes should be in the same coordinate system

## Output

The inference returns a `GraspGroup` object containing:
- Grasp poses (position and orientation)
- Grasp scores (confidence values)
- Grasp parameters (width, height, depth)
- Object associations

## Troubleshooting

### Common Issues

1. **"No grasps found"**
   - Check point cloud quality and density
   - Verify objects are visible in the scene
   - Adjust collision detection parameters

2. **Model loading errors**
   - Verify checkpoint file path and format
   - Ensure all model dependencies are available
   - Check CUDA availability for GPU inference

3. **Point cloud loading issues**
   - Verify file paths and formats
   - Check point cloud contains valid 3D coordinates
   - Ensure sufficient point density

### Performance Tips

- Use GPU acceleration when available
- Adjust `num_point` based on scene complexity
- Consider voxel downsampling for large point clouds
- Use appropriate collision detection thresholds

## API Reference

### GN Class Methods

#### `__init__(checkpoint_path, **kwargs)`
Initialize the pick-and-place model.

#### `inference(o3d_pcd_initial, o3d_pcd_target)`
Perform grasp inference on dual point clouds.

**Parameters:**
- `o3d_pcd_initial`: Open3D point cloud of initial scene
- `o3d_pcd_target`: Open3D point cloud of target scene

**Returns:**
- `GraspGroup`: Collection of grasp poses with scores

#### `get_and_process_data(cloud)`
Process point cloud data for model input.

#### `get_grasps(end_points_initial, end_points_target)`
Generate grasps from processed point cloud features.

#### `collision_detection(gg, cloud)`
Filter grasps based on collision detection.

### Utility Functions

#### `vis_grasps(gg, cloud, save_path=None, max_grasps=50)`
Visualize grasp poses on point cloud.

**Parameters:**
- `gg`: GraspGroup object
- `cloud`: Open3D point cloud
- `save_path`: Optional path to save visualization
- `max_grasps`: Maximum number of grasps to display

## Examples

### Loading Different Point Cloud Formats

```python
# Load PCD file
pcd = o3d.io.read_point_cloud("scene.pcd")

# Load PLY file
pcd = o3d.io.read_point_cloud("scene.ply")

# Ensure colors are present
if not pcd.has_colors():
    pcd.colors = o3d.utility.Vector3dVector(
        np.ones((len(pcd.points), 3)) * 0.5
    )
```

### Saving Visualization Results

```python
# Save as PLY point cloud
vis_grasps(grasps, scene_pcd, save_path="results.ply")

# Save as PCD point cloud
vis_grasps(grasps, scene_pcd, save_path="results.pcd")

# Save as OBJ mesh
vis_grasps(grasps, scene_pcd, save_path="results.obj")
```

### Analyzing Grasp Results

```python
# Sort grasps by score
grasps.sort_by_score()

# Get best grasp
best_grasp = grasps[0]
print(f"Score: {best_grasp.score}")
print(f"Position: {best_grasp.translation}")
print(f"Width: {best_grasp.width}")

# Get statistics
scores = grasps.scores
print(f"Average score: {scores.mean()}")
print(f"Score range: {scores.min()} - {scores.max()}")
```

## License

This code follows the same license as the main GraspNet project. 