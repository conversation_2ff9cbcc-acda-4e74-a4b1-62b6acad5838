#!/usr/bin/env python3
"""
Simple Pick and Place Example

A minimal example showing how to use the pick_and_place module.
This script demonstrates the basic API usage without extensive error handling.
"""

import numpy as np
import open3d as o3d
from scipy.spatial.transform import Rotation as R
from pick_and_place import G<PERSON>, vis_grasps, transform_to_robot_coordinate

def create_sample_point_cloud():
    """
    Create a sample point cloud for testing purposes.
    In practice, you would load real point clouds from files.
    """
    # Create a simple box-shaped point cloud
    points = []
    colors = []
    
    # Generate random points in a box
    for _ in range(1000):
        x = np.random.uniform(-0.1, 0.1)
        y = np.random.uniform(-0.1, 0.1)
        z = np.random.uniform(0.0, 0.1)
        points.append([x, y, z])
        colors.append([0.5, 0.5, 0.5])  # Gray color
    
    # Create Open3D point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(np.array(points))
    pcd.colors = o3d.utility.Vector3dVector(np.array(colors))
    
    return pcd

def main():
    """
    Simple demonstration of pick and place functionality.
    """
    print("=== Simple Pick and Place Demo ===")
    
    # NOTE: Update this path to your actual checkpoint file
    checkpoint_path = "logs/log_rs/checkpoint_rs.tar"
    
    # Check if checkpoint exists
    import os
    if not os.path.exists(checkpoint_path):
        print(f"Checkpoint not found at: {checkpoint_path}")
        print("Please update the checkpoint_path variable.")
        print("For testing purposes, creating sample point clouds...")
        
        # Create sample point clouds for demonstration
        initial_pcd = create_sample_point_cloud()
        target_pcd = create_sample_point_cloud()
        
        print("Sample point clouds created.")
        print("To run actual inference, you need:")
        print("1. A trained model checkpoint (.tar file)")
        print("2. Real point cloud files (.pcd or .ply)")
        print("\nExample usage:")
        print("```python")
        print("# Initialize the model")
        print("graspnet = GN(checkpoint_path='your_model.tar')")
        print("")
        print("# Load point clouds")
        print("initial_pcd = o3d.io.read_point_cloud('initial_scene.pcd')")
        print("target_pcd = o3d.io.read_point_cloud('target_scene.pcd')")
        print("")
        print("# Perform inference")
        print("grasps = graspnet.inference(initial_pcd, target_pcd)")
        print("")
        print("# Visualize results")
        print("vis_grasps(grasps, initial_pcd)")
        print("```")
        return
    
    # Initialize GraspNet model
    print("Initializing GraspNet model...")
    graspnet = GN(checkpoint_path=checkpoint_path)
    
    # Load or create point clouds
    # Replace these with your actual point cloud files
    target_pcd_path = "tmp_ascii.ply" # scene
    initial_pcd_path = "tmp_ascii_target.ply" # model
    
    try:
        initial_pcd = o3d.io.read_point_cloud(initial_pcd_path)
        target_pcd = o3d.io.read_point_cloud(target_pcd_path)
        
        # Ensure point clouds have colors
        if not initial_pcd.has_colors():
            initial_pcd.colors = o3d.utility.Vector3dVector(
                np.ones((len(initial_pcd.points), 3)) * 0.5
            )
        if not target_pcd.has_colors():
            target_pcd.colors = o3d.utility.Vector3dVector(
                np.ones((len(target_pcd.points), 3)) * 0.5
            )
        
    except:
        print("Could not load point cloud files. Creating sample data...")
        initial_pcd = create_sample_point_cloud() # model
        target_pcd = create_sample_point_cloud() # scene
    
    # Perform inference
    print("Running inference...")
    grasps = graspnet.inference(initial_pcd, target_pcd)
    
    # Display results
    print(f"Found {len(grasps)} grasp poses")
    
    if len(grasps) > 0:
        # Sort by score and show best grasp
        grasps.sort_by_score()
        best_grasp = grasps[0]
        
        print(f"Best grasp score: {best_grasp.score:.4f}")
        print(f"Best grasp width: {best_grasp.width:.4f}")
        
        print("=== Camera Coordinate System ===")
        print(f"Best grasp translation: {best_grasp.translation}")
        print(f"Best grasp rotation matrix: {best_grasp.rotation_matrix}")

        print("=== Robot Coordinate System ===")
        camera_extrinsic = np.eye(4)
        robot_trans, robot_rot, robot_euler = transform_to_robot_coordinate(camera_extrinsic, best_grasp.translation, best_grasp.rotation_matrix)
        print(f"Best grasp translation: {robot_trans}")
        print(f"Best grasp rotation matrix: {robot_rot}")
        print(f"Best grasp rotation (Euler XYZ degrees): [{robot_euler[0]:.2f}, {robot_euler[1]:.2f}, {robot_euler[2]:.2f}]")

        # Visualize results
        print("Visualizing grasps...")
        # vis_grasps(grasps, initial_pcd)
        
        # Example: Save with both camera and world coordinates
        # In practice, you should provide the actual camera extrinsic matrix
        vis_grasps(grasps, target_pcd, save_dir='results/0707_T7', max_grasps=20, camera_extrinsic=camera_extrinsic) # scene + gripper

    
    print("Demo completed!")

if __name__ == "__main__":
    main() 